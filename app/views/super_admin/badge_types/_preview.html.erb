<%# Badge Preview Component %>
<%# This partial provides a live preview of the badge as it's being customized %>

<div class="bg-white border border-stone-200 rounded-lg p-6 shadow-sm">
  <div class="mb-4">
    <h3 class="text-lg font-medium text-stone-900 mb-2">Live Preview</h3>
    <p class="text-sm text-stone-600">See how your badge will appear to users</p>
  </div>

  <%# Main Badge Preview %>
  <div class="space-y-6">
    <%# Full Size Badge Preview %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Full Size Badge</h4>
      <div class="flex items-center justify-center p-6 bg-stone-50 rounded-lg border border-stone-200">
        <span class="badge-preview-element inline-flex items-center px-4 py-2 rounded-lg text-lg font-semibold shadow-sm border transition-all duration-200"
              style="background-color: #3B82F6; color: #FFFFFF; border-color: #2563EB;">
          <i class="badge-preview-icon fas fa-star mr-2"></i>
          <span class="badge-preview-name">Sample Badge</span>
        </span>
      </div>
    </div>

    <%# Compact Badge Preview %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Compact Size (Search Results)</h4>
      <div class="flex items-center justify-center p-4 bg-stone-50 rounded-lg border border-stone-200">
        <span class="badge-preview-element inline-flex items-center px-2 py-1 rounded-md text-sm font-medium shadow-sm border transition-all duration-200"
              style="background-color: #3B82F6; color: #FFFFFF; border-color: #2563EB;">
          <i class="badge-preview-icon fas fa-star mr-1.5 text-xs"></i>
          <span class="badge-preview-name">Sample Badge</span>
        </span>
      </div>
    </div>

    <%# Badge Description Preview %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Description</h4>
      <div class="p-4 bg-stone-50 rounded-lg border border-stone-200">
        <p class="badge-preview-description text-sm text-stone-600 italic">
          This is a sample badge description
        </p>
      </div>
    </div>

    <%# Color Contrast Warning %>
    <div class="contrast-warning hidden p-3 bg-amber-50 border border-amber-200 rounded-lg">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-amber-800">Accessibility Warning</h3>
          <div class="mt-1 text-sm text-amber-700">
            <!-- Warning message will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>

    <%# Badge Properties Summary %>
    <div>
      <h4 class="text-sm font-medium text-stone-700 mb-3">Badge Properties</h4>
      <div class="bg-stone-50 rounded-lg border border-stone-200 p-4">
        <dl class="grid grid-cols-1 gap-3 sm:grid-cols-2">
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Background Color</dt>
            <dd class="mt-1 flex items-center">
              <div class="badge-preview-bg-color w-4 h-4 rounded border border-stone-300 mr-2" 
                   style="background-color: #3B82F6;"></div>
              <span class="badge-preview-bg-value text-sm text-stone-900 font-mono">#3B82F6</span>
            </dd>
          </div>
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Text Color</dt>
            <dd class="mt-1 flex items-center">
              <div class="badge-preview-text-color w-4 h-4 rounded border border-stone-300 mr-2" 
                   style="background-color: #FFFFFF;"></div>
              <span class="badge-preview-text-value text-sm text-stone-900 font-mono">#FFFFFF</span>
            </dd>
          </div>
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Icon Class</dt>
            <dd class="mt-1">
              <span class="badge-preview-icon-value text-sm text-stone-900 font-mono">fas fa-star</span>
            </dd>
          </div>
          <div>
            <dt class="text-xs font-medium text-stone-500 uppercase tracking-wide">Priority</dt>
            <dd class="mt-1">
              <span class="badge-preview-priority-value text-sm text-stone-900">0</span>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <%# Preview Actions %>
    <div class="flex justify-between items-center pt-4 border-t border-stone-200">
      <div class="text-xs text-stone-500">
        Preview updates automatically as you type
      </div>
      <button type="button" 
              data-action="click->badge-preview#resetPreview"
              class="inline-flex items-center px-3 py-1.5 border border-stone-300 shadow-sm text-xs font-medium rounded text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg class="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Reset to Defaults
      </button>
    </div>
  </div>
</div>

<%# Additional CSS for enhanced preview styling %>
<style>
  .badge-preview-element {
    /* Enhanced badge styling for preview */
    backdrop-filter: blur(8px);
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
  }

  .badge-preview-element::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
  }

  .badge-preview-element:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Ensure icon fonts are loaded */
  .badge-preview-icon {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Pro", sans-serif;
    font-weight: 900;
  }

  /* Color swatch styling */
  .badge-preview-bg-color,
  .badge-preview-text-color {
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  }
</style>
