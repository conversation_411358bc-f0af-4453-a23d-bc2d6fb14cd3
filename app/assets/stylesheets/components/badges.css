/* Badge Component Styles */
@layer components {
  /* CSS Custom Properties for Holographic Effects */
  .badge,
  .badge-compact {
    --holo-primary: 0deg;
    --holo-secondary: 120deg;
    --holo-tertiary: 240deg;
    --holo-intensity: 0.6;
    --holo-speed: 3s;
    --holo-shift: 0deg;
    --metallic-base: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 25%, #e5e7eb 50%, #d1d5db 75%, #e5e7eb 100%);
  }

  /* Base badge styles with glassy modern appearance and holographic foundation */
  .badge {
    @apply relative inline-flex items-center font-medium rounded-lg border shadow-sm;
    @apply backdrop-filter backdrop-blur-sm bg-opacity-90;
    @apply transition-all duration-200 ease-out;
    @apply transform-gpu will-change-transform;
    @apply cursor-default overflow-hidden;

    /* Default size (medium) */
    @apply px-3 py-1.5 text-sm;

    /* Holographic base layer */
    background-image:
      var(--metallic-base),
      linear-gradient(var(--holo-shift),
        hsl(var(--holo-primary), 70%, 60%) 0%,
        hsl(var(--holo-secondary), 70%, 60%) 33%,
        hsl(var(--holo-tertiary), 70%, 60%) 66%,
        hsl(var(--holo-primary), 70%, 60%) 100%);
    background-blend-mode: overlay, normal;
    background-size: 100% 100%, 200% 200%;
    animation: holographic-shift var(--holo-speed) ease-in-out infinite;
  }

  /* Compact badge variant for search results */
  .badge-compact {
    @apply relative inline-flex items-center font-medium rounded-md border shadow-sm;
    @apply backdrop-filter backdrop-blur-sm bg-opacity-90;
    @apply transition-all duration-200 ease-out;
    @apply transform-gpu will-change-transform;
    @apply cursor-default overflow-hidden;

    /* Compact size */
    @apply px-2 py-0.5 text-xs;
    max-width: 120px;

    /* Holographic base layer for compact badges */
    background-image:
      var(--metallic-base),
      linear-gradient(var(--holo-shift),
        hsl(var(--holo-primary), 70%, 60%) 0%,
        hsl(var(--holo-secondary), 70%, 60%) 33%,
        hsl(var(--holo-tertiary), 70%, 60%) 66%,
        hsl(var(--holo-primary), 70%, 60%) 100%);
    background-blend-mode: overlay, normal;
    background-size: 100% 100%, 200% 200%;
    animation: holographic-shift var(--holo-speed) ease-in-out infinite;
  }

  /* Sparkle effects container */
  .badge .sparkle-container,
  .badge-compact .sparkle-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    border-radius: inherit;
    z-index: 3;
  }

  /* Individual sparkle elements - Enhanced visibility */
  .badge .sparkle,
  .badge-compact .sparkle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: radial-gradient(circle,
      rgba(255,255,255,1) 0%,
      rgba(255,255,255,0.8) 30%,
      rgba(255,255,255,0.4) 60%,
      transparent 100%);
    border-radius: 50%;
    animation: sparkle 1.5s ease-in-out infinite;
    pointer-events: none;
    box-shadow: 0 0 8px rgba(255,255,255,0.6);
  }

  .badge .sparkle:nth-child(1) { top: 20%; left: 15%; animation-delay: 0s; }
  .badge .sparkle:nth-child(2) { top: 60%; left: 80%; animation-delay: 0.5s; }
  .badge .sparkle:nth-child(3) { top: 30%; left: 60%; animation-delay: 1s; }

  .badge-compact .sparkle:nth-child(1) { top: 25%; left: 20%; animation-delay: 0s; }
  .badge-compact .sparkle:nth-child(2) { top: 70%; left: 75%; animation-delay: 0.7s; }

  /* Light ray effect - Enhanced visibility */
  .badge .light-ray,
  .badge-compact .light-ray {
    position: absolute;
    top: 0;
    left: -50%;
    width: 30%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255,255,255,0.7) 30%,
      rgba(255,255,255,0.9) 50%,
      rgba(255,255,255,0.7) 70%,
      transparent 100%);
    animation: light-ray 2.5s ease-in-out infinite;
    animation-delay: 0.5s;
    pointer-events: none;
    z-index: 4;
    filter: blur(0.5px);
    box-shadow: 0 0 10px rgba(255,255,255,0.5);
  }

  /* Badge icon styling with enhanced holographic effects */
  .badge-icon {
    @apply transition-transform duration-200 ease-out;
    position: relative;
    z-index: 5;
    filter: drop-shadow(0 0 2px rgba(255,255,255,0.3));
  }

  /* Badge name text with enhanced visibility */
  .badge-name {
    @apply font-medium;
    position: relative;
    z-index: 5;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }

  /* Compact badge name with truncation */
  .badge-compact .badge-name {
    @apply truncate;
    max-width: 80px;
  }

  /* Size variants for full badges */
  .badge-sm {
    @apply px-2 py-1 text-xs;
  }

  .badge-sm .badge-icon {
    @apply text-xs mr-1;
  }

  .badge-lg {
    @apply px-4 py-2.5 text-base;
  }

  .badge-lg .badge-icon {
    @apply text-base mr-2;
  }

  /* Default medium size icon spacing */
  .badge .badge-icon {
    @apply text-sm mr-1.5;
  }

  /* Holographic keyframe animations */
  @keyframes holographic-shift {
    0% {
      --holo-shift: 45deg;
      background-position: 0% 0%, 0% 0%;
    }
    25% {
      --holo-shift: 135deg;
      background-position: 0% 0%, 25% 25%;
    }
    50% {
      --holo-shift: 225deg;
      background-position: 0% 0%, 50% 50%;
    }
    75% {
      --holo-shift: 315deg;
      background-position: 0% 0%, 75% 75%;
    }
    100% {
      --holo-shift: 405deg;
      background-position: 0% 0%, 100% 100%;
    }
  }

  @keyframes sparkle {
    0%, 100% {
      opacity: 0;
      transform: scale(0) rotate(0deg);
    }
    50% {
      opacity: 1;
      transform: scale(1) rotate(180deg);
    }
  }

  @keyframes light-ray {
    0% {
      transform: translateX(-100%) skewX(-15deg);
      opacity: 0;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      transform: translateX(200%) skewX(-15deg);
      opacity: 0;
    }
  }

  @keyframes prismatic-pulse {
    0%, 100% {
      filter: hue-rotate(0deg) saturate(1);
    }
    33% {
      filter: hue-rotate(120deg) saturate(1.2);
    }
    66% {
      filter: hue-rotate(240deg) saturate(1.1);
    }
  }

  /* Enhanced glassy effect overlay with holographic elements */
  .badge::before,
  .badge-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255,255,255,0.2) 0%,
      rgba(255,255,255,0.1) 25%,
      rgba(255,255,255,0.05) 50%,
      rgba(255,255,255,0.1) 75%,
      rgba(255,255,255,0.15) 100%);
    pointer-events: none;
    border-radius: inherit;
    z-index: 1;
  }

  /* Holographic rainbow overlay - Enhanced visibility */
  .badge::after,
  .badge-compact::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(255, 0, 150, 0.25) 0%,
      rgba(0, 255, 255, 0.25) 20%,
      rgba(255, 255, 0, 0.25) 40%,
      rgba(255, 0, 255, 0.25) 60%,
      rgba(0, 255, 150, 0.25) 80%,
      rgba(255, 100, 0, 0.25) 100%);
    background-size: 400% 400%;
    animation: prismatic-pulse 3s ease-in-out infinite;
    pointer-events: none;
    border-radius: inherit;
    opacity: var(--holo-intensity);
    z-index: 2;
    mix-blend-mode: overlay;
  }

  /* Enhanced hover effects with holographic intensification */
  .badge:hover {
    @apply shadow-lg;
    transform: translateY(-3px) scale(1.05);
    --holo-intensity: 1.2;
    --holo-speed: 1s;
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.2),
      0 0 40px rgba(255, 255, 255, 0.4),
      0 0 20px rgba(97, 0, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }

  .badge:hover::after {
    animation-duration: 1.5s;
    opacity: calc(var(--holo-intensity) * 1.8);
    background-size: 200% 200%;
  }

  .badge:hover .sparkle {
    animation-duration: 0.8s;
    width: 8px;
    height: 8px;
    box-shadow: 0 0 12px rgba(255,255,255,0.9);
  }

  .badge:hover .light-ray {
    animation-duration: 1s;
    opacity: 0.9;
    width: 40%;
  }

  .badge-compact:hover {
    @apply shadow-md;
    transform: translateY(-1px) scale(1.03);
    --holo-intensity: 0.8;
    --holo-speed: 2s;
    box-shadow:
      0 6px 15px rgba(0, 0, 0, 0.12),
      0 0 20px rgba(255, 255, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }

  .badge-compact:hover::after {
    animation-duration: 2.5s;
    opacity: calc(var(--holo-intensity) * 1.3);
  }

  .badge-compact:hover .sparkle {
    animation-duration: 1.2s;
  }

  /* Active/pressed state for enhanced interactivity */
  .badge:active,
  .badge-compact:active {
    transform: translateY(0) scale(0.98);
    --holo-intensity: 1.2;
    transition: all 0.1s ease-out;
  }

  /* Breathing animation for active badges */
  .badge.active,
  .badge-compact.active {
    animation: holographic-shift var(--holo-speed) ease-in-out infinite,
               breathing 2s ease-in-out infinite;
  }

  @keyframes breathing {
    0%, 100% {
      transform: scale(1);
      --holo-intensity: 0.6;
    }
    50% {
      transform: scale(1.02);
      --holo-intensity: 0.9;
    }
  }

  /* Tooltip styles */
  .badge-tooltip,
  .badge-tooltip-compact {
    @apply absolute z-50 invisible opacity-0 transition-all duration-200 ease-out;
    @apply bg-stone-900 text-white text-xs rounded-md px-2 py-1;
    @apply pointer-events-none whitespace-nowrap;
    @apply mt-8 left-1/2 transform -translate-x-1/2;
  }

  .badge-tooltip-compact {
    @apply rounded-lg px-3 py-2 shadow-lg;
    max-width: 200px;
    white-space: normal;
  }

  /* Show tooltips on hover */
  .badge:hover .badge-tooltip,
  .badge-compact:hover .badge-tooltip-compact {
    @apply visible opacity-100;
  }

  /* Tooltip arrows */
  .badge-tooltip::after,
  .badge-tooltip-compact::after {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: theme('colors.stone.900');
  }

  /* Predefined badge color schemes for common use cases */
  .badge-verified {
    @apply bg-blue-100 bg-opacity-20 text-blue-800 border-blue-200;
  }

  .badge-choice {
    @apply bg-purple-100 bg-opacity-20 text-purple-800 border-purple-200;
  }

  .badge-premium {
    @apply bg-amber-100 bg-opacity-20 text-amber-800 border-amber-200;
  }

  .badge-expert {
    @apply bg-emerald-100 bg-opacity-20 text-emerald-800 border-emerald-200;
  }

  .badge-featured {
    @apply bg-rose-100 bg-opacity-20 text-rose-800 border-rose-200;
  }

  .badge-new {
    @apply bg-green-100 bg-opacity-20 text-green-800 border-green-200;
  }

  /* Icon-only mode for very compact display */
  .badge-compact[data-icon-only="true"] {
    @apply justify-center;
    max-width: 24px;
    min-width: 24px;
  }

  .badge-compact[data-icon-only="true"] .badge-icon {
    @apply mr-0;
  }

  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .badge,
    .badge-compact {
      transition: none !important;
      animation: none !important;
      --holo-speed: 0s;
    }

    .badge::after,
    .badge-compact::after {
      animation: none !important;
      opacity: 0.3 !important;
    }

    .badge .sparkle,
    .badge-compact .sparkle,
    .badge .light-ray,
    .badge-compact .light-ray {
      animation: none !important;
      opacity: 0 !important;
    }

    .badge-icon {
      transition: none !important;
      filter: none !important;
    }

    .badge:hover,
    .badge-compact:hover {
      transform: none !important;
      --holo-intensity: 0.3;
    }

    .badge.active,
    .badge-compact.active {
      animation: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .badge,
    .badge-compact {
      @apply border-2;
      backdrop-filter: none;
      background-opacity: 1 !important;
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .badge-tooltip,
    .badge-tooltip-compact {
      @apply bg-stone-100 text-stone-900;
    }

    .badge-tooltip::after,
    .badge-tooltip-compact::after {
      border-bottom-color: theme('colors.stone.100');
    }
  }

  /* Mobile responsiveness */
  @media (max-width: 640px) {
    .badge-compact {
      max-width: 100px;
    }
    
    .badge-compact .badge-name {
      max-width: 60px;
    }

    /* Slightly smaller badges on mobile */
    .badge {
      @apply px-2.5 py-1 text-xs;
    }

    .badge .badge-icon {
      @apply text-xs mr-1;
    }
  }

  /* Print styles */
  @media print {
    .badge,
    .badge-compact {
      background: white !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
      transform: none !important;
    }
    
    .badge::before,
    .badge-compact::before {
      display: none !important;
    }
    
    .badge-tooltip,
    .badge-tooltip-compact {
      display: none !important;
    }
  }

  /* Focus styles for accessibility */
  .badge:focus,
  .badge-compact:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }

  /* Loading state for dynamic badges */
  .badge-loading {
    @apply animate-pulse bg-stone-200 text-transparent;
  }

  .badge-loading .badge-icon {
    @apply text-transparent;
  }
}
