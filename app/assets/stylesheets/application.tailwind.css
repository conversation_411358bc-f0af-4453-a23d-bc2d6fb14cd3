@tailwind base;
@tailwind components;

/* Import component styles */
@import "components/badges.css";

@tailwind utilities;

@layer components {
  .scrollbar-gutter-stable {
    scrollbar-gutter: stable;
  }

  .message-sent {
    @apply text-right;
  }

  .message-received {
    @apply text-left;
  }

  .message-sent .message-bubble {
    @apply text-stone-100 bg-stone-900;
  }

  .message-received .message-bubble {
    @apply text-stone-900 bg-stone-100;
  }

  .messages-container {
    @apply p-4 overflow-y-auto h-96;
    scroll-behavior: smooth;
  }

  /* Sidebar styles */
  .sidebar-collapsed {
    @apply w-16 !important;
  }

  .sidebar-expanded {
    @apply items-center;
    @apply w-64 !important;
  }

  /* Hide text when sidebar is collapsed */
  .sidebar-collapsed .sidebar-text {
    @apply hidden;
  }
  
  /* Ensure the user avatar is visible in collapsed view */
  .sidebar-collapsed .border-t {
    @apply border-stone-200;
  }
  
  /* Keep avatar visible and centered in collapsed view */
  .sidebar-collapsed img.rounded-full {
    @apply mx-auto;
  }
  
  /* Adjust footer padding in collapsed view */
  .sidebar-collapsed .p-4.mt-auto {
    @apply px-2 py-4;
  }
  
  /* Style the user avatar button in collapsed mode */
  .sidebar-collapsed .user-avatar-button > div {
    @apply mx-auto;
  }
  
  .sidebar-collapsed .user-avatar-button svg {
    @apply hidden;
  }
  
  /* Dropdown menu positioning */
  .dropdown-menu {
    min-width: 220px;
    position: fixed !important;
    bottom: 70px;
    left: 70px;
    transform-origin: bottom left;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* Adjust dropdown position in collapsed view */
  .sidebar-collapsed .dropdown-menu {
    left: 60px;
  }

  /* Reset padding for collapsed nav */
  .sidebar-collapsed nav {
    @apply px-2; /* Adjust padding slightly for centered icons */
  }

  /* Style the dedicated sidebar toggle button */
  .sidebar-toggle-btn {
    @apply border border-stone-200;
  }
  
  .sidebar-collapsed .sidebar-toggle-btn {
    @apply p-2;
  }

  /* Style nav links when collapsed */
  .sidebar-collapsed nav a {
    @apply flex p-2 mb-2 border rounded-md border-stone-200; /* Add padding and visible border */
  }

  .sidebar-collapsed nav a:hover {
    @apply bg-stone-200 border-stone-100; /* Add background and border on hover */
  }

  /* Adjust main content margin when sidebar is collapsed/expanded */
  .sidebar-collapsed-main {
    @apply ml-16 !important;
  }

  .sidebar-expanded-main {
    @apply ml-64 !important;
  }

  /* Responsive adjustments */
  @media (max-width: 1023px) {
    .sidebar-expanded-main {
      @apply ml-0 !important;
    }
  }
}
