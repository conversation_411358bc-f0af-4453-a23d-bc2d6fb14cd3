@tailwind base;
@tailwind components;

@tailwind utilities;

@layer components {
  /* SIMPLIFIED HOLOGRAPHIC BADGE EFFECTS - GUARANTEED VISIBILITY */
  .badge,
  .badge-compact {
    position: relative !important;
    overflow: visible !important;
    isolation: isolate !important;
  }

  /* Visible shimmer effect without mix-blend-mode */
  .badge:before,
  .badge-compact:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    pointer-events: none;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.9) 50%,
      transparent 70%
    );
    background-size: 200% 200%;
    background-position: -100% -100%;
    opacity: 0;
    transition: all 0.6s ease;
    z-index: 1;
  }

  /* Animated rainbow border effect */
  .badge:after,
  .badge-compact:after {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: inherit;
    pointer-events: none;
    background: linear-gradient(
      45deg,
      #ff0000, #ff4000, #ff8000, #ffbf00, #ffff00, #bfff00,
      #80ff00, #40ff00, #00ff00, #00ff40, #00ff80, #00ffbf,
      #00ffff, #00bfff, #0080ff, #0040ff, #0000ff, #4000ff,
      #8000ff, #bf00ff, #ff00ff, #ff00bf, #ff0080, #ff0040
    );
    background-size: 400% 400%;
    opacity: 0;
    animation: rainbow-border 2s linear infinite;
    z-index: -1;
    transition: opacity 0.3s ease;
  }

  /* Hover effects - GUARANTEED VISIBLE */
  .badge:hover:before,
  .badge-compact:hover:before {
    background-position: 100% 100%;
    opacity: 0.8 !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
  }

  .badge:hover:after,
  .badge-compact:hover:after {
    opacity: 0.9 !important;
  }

  /* Active state effects - MAXIMUM VISIBILITY */
  .badge.holo-active:before,
  .badge-compact.holo-active:before {
    opacity: 1 !important;
    background-position: 100% 100%;
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.8);
  }

  .badge.holo-active:after,
  .badge-compact.holo-active:after {
    opacity: 1 !important;
    box-shadow: 0 0 20px rgba(255, 100, 200, 0.8);
  }

  /* Rainbow border animation */
  @keyframes rainbow-border {
    0% { background-position: 0% 0%; }
    25% { background-position: 100% 0%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
    100% { background-position: 0% 0%; }
  }

  .scrollbar-gutter-stable {
    scrollbar-gutter: stable;
  }

  .message-sent {
    @apply text-right;
  }

  .message-received {
    @apply text-left;
  }

  .message-sent .message-bubble {
    @apply text-stone-100 bg-stone-900;
  }

  .message-received .message-bubble {
    @apply text-stone-900 bg-stone-100;
  }

  .messages-container {
    @apply p-4 overflow-y-auto h-96;
    scroll-behavior: smooth;
  }

  /* Sidebar styles */
  .sidebar-collapsed {
    @apply w-16 !important;
  }

  .sidebar-expanded {
    @apply items-center;
    @apply w-64 !important;
  }

  /* Hide text when sidebar is collapsed */
  .sidebar-collapsed .sidebar-text {
    @apply hidden;
  }
  
  /* Ensure the user avatar is visible in collapsed view */
  .sidebar-collapsed .border-t {
    @apply border-stone-200;
  }
  
  /* Keep avatar visible and centered in collapsed view */
  .sidebar-collapsed img.rounded-full {
    @apply mx-auto;
  }
  
  /* Adjust footer padding in collapsed view */
  .sidebar-collapsed .p-4.mt-auto {
    @apply px-2 py-4;
  }
  
  /* Style the user avatar button in collapsed mode */
  .sidebar-collapsed .user-avatar-button > div {
    @apply mx-auto;
  }
  
  .sidebar-collapsed .user-avatar-button svg {
    @apply hidden;
  }
  
  /* Dropdown menu positioning */
  .dropdown-menu {
    min-width: 220px;
    position: fixed !important;
    bottom: 70px;
    left: 70px;
    transform-origin: bottom left;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* Adjust dropdown position in collapsed view */
  .sidebar-collapsed .dropdown-menu {
    left: 60px;
  }

  /* Reset padding for collapsed nav */
  .sidebar-collapsed nav {
    @apply px-2; /* Adjust padding slightly for centered icons */
  }

  /* Style the dedicated sidebar toggle button */
  .sidebar-toggle-btn {
    @apply border border-stone-200;
  }
  
  .sidebar-collapsed .sidebar-toggle-btn {
    @apply p-2;
  }

  /* Style nav links when collapsed */
  .sidebar-collapsed nav a {
    @apply flex p-2 mb-2 border rounded-md border-stone-200; /* Add padding and visible border */
  }

  .sidebar-collapsed nav a:hover {
    @apply bg-stone-200 border-stone-100; /* Add background and border on hover */
  }

  /* Adjust main content margin when sidebar is collapsed/expanded */
  .sidebar-collapsed-main {
    @apply ml-16 !important;
  }

  .sidebar-expanded-main {
    @apply ml-64 !important;
  }

  /* Responsive adjustments */
  @media (max-width: 1023px) {
    .sidebar-expanded-main {
      @apply ml-0 !important;
    }
  }
}
