import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="badge"
export default class extends Controller {
  static targets = ["badge"];

  connect() {
    this.checkReducedMotion();
    this.addEventListeners();
    this.initializeHolographicEffects();
  }

  disconnect() {
    this.removeEventListeners();
    this.cleanupHolographicEffects();
  }

  addEventListeners() {
    this.badgeTargets.forEach((badge) => {
      badge.addEventListener("mousemove", this.handleMouseMove);
      badge.addEventListener("mouseleave", this.handleMouseLeave);
      badge.addEventListener("mouseenter", this.handleMouseEnter);
    });
  }

  removeEventListeners() {
    this.badgeTargets.forEach((badge) => {
      badge.removeEventListener("mousemove", this.handleMouseMove);
      badge.removeEventListener("mouseleave", this.handleMouseLeave);
      badge.removeEventListener("mouseenter", this.handleMouseEnter);
    });
  }

  checkReducedMotion() {
    // Check if user prefers reduced motion for accessibility
    this.prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
  }

  initializeHolographicEffects() {
    if (this.prefersReducedMotion) return;

    this.badgeTargets.forEach((badge) => {
      this.createSparkleElements(badge);
      this.createLightRayElement(badge);
      this.setupHolographicVariables(badge);
    });
  }

  cleanupHolographicEffects() {
    this.badgeTargets.forEach((badge) => {
      // Remove dynamically created elements
      const sparkleContainer = badge.querySelector(".sparkle-container");
      const lightRay = badge.querySelector(".light-ray");

      if (sparkleContainer) sparkleContainer.remove();
      if (lightRay) lightRay.remove();
    });
  }

  createSparkleElements(badge) {
    // Create sparkle container if it doesn't exist
    let sparkleContainer = badge.querySelector(".sparkle-container");
    if (!sparkleContainer) {
      sparkleContainer = document.createElement("div");
      sparkleContainer.className = "sparkle-container";
      badge.appendChild(sparkleContainer);
    }

    // Create sparkle elements
    const sparkleCount = badge.classList.contains("badge-compact") ? 2 : 3;
    for (let i = 0; i < sparkleCount; i++) {
      const sparkle = document.createElement("div");
      sparkle.className = "sparkle";
      sparkleContainer.appendChild(sparkle);
    }
  }

  createLightRayElement(badge) {
    // Create light ray element if it doesn't exist
    let lightRay = badge.querySelector(".light-ray");
    if (!lightRay) {
      lightRay = document.createElement("div");
      lightRay.className = "light-ray";
      badge.appendChild(lightRay);
    }
  }

  setupHolographicVariables(badge) {
    // Initialize CSS custom properties for holographic effects
    badge.style.setProperty("--holo-primary", "0deg");
    badge.style.setProperty("--holo-secondary", "120deg");
    badge.style.setProperty("--holo-tertiary", "240deg");
    badge.style.setProperty("--holo-intensity", "0.6");
    badge.style.setProperty("--holo-speed", "3s");
  }

  handleMouseMove = (e) => {
    // Skip animations if user prefers reduced motion
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    const rect = badge.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    const deltaX = (x - centerX) / centerX;
    const deltaY = (y - centerY) / centerY;

    // Enhanced tilt effect with more pronounced 3D transformation
    const rotateX = deltaY * -8;
    const rotateY = deltaX * 8;
    const scale = 1.05;

    badge.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(${scale}, ${scale}, ${scale})`;

    // Apply parallax effect to the icon with enhanced movement
    const icon = badge.querySelector(".badge-icon");
    if (icon) {
      const parallaxX = deltaX * 4;
      const parallaxY = deltaY * 4;
      icon.style.transform = `translateX(${parallaxX}px) translateY(${parallaxY}px) rotateZ(${
        deltaX * 5
      }deg)`;
    }

    // Dynamic holographic color shifting based on mouse position
    this.updateHolographicColors(badge, deltaX, deltaY);

    // Enhanced glow effect with holographic colors
    const holographicGlow = this.getHolographicGlow(deltaX, deltaY);
    badge.style.boxShadow = `
      0 12px 30px rgba(0, 0, 0, 0.2),
      0 0 25px ${holographicGlow},
      inset 0 1px 0 rgba(255, 255, 255, 0.4)
    `;

    // Update light ray position based on mouse movement
    this.updateLightRayPosition(badge, deltaX, deltaY);
  };

  handleMouseEnter = (e) => {
    // Skip animations if user prefers reduced motion
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;

    // Add initial hover state
    badge.style.transition = "box-shadow 0.2s ease-out";
    badge.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
  };

  handleMouseLeave = (e) => {
    const badge = e.currentTarget;

    // Reset transform and effects
    badge.style.transform =
      "perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)";
    badge.style.transition =
      "transform 0.3s ease-out, box-shadow 0.3s ease-out";
    badge.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.1)";

    // Reset icon position
    const icon = badge.querySelector(".badge-icon");
    if (icon) {
      icon.style.transform = "translateX(0) translateY(0)";
      icon.style.transition = "transform 0.3s ease-out";
    }
  };

  // Helper method to extract RGB values from background color for glow effect
  extractRGB(color) {
    // Handle hex colors
    if (color.startsWith("#")) {
      const hex = color.slice(1);
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      return `${r}, ${g}, ${b}`;
    }

    // Handle rgb/rgba colors
    const match = color.match(/rgba?\(([^)]+)\)/);
    if (match) {
      const values = match[1]
        .split(",")
        .slice(0, 3)
        .map((v) => v.trim());
      return values.join(", ");
    }

    // Fallback to a neutral color
    return "59, 130, 246"; // Blue-500 RGB
  }

  // Public method to refresh badge animations (useful for dynamic content)
  refresh() {
    this.disconnect();
    this.connect();
  }

  // Public method to disable animations (useful for testing or accessibility)
  disableAnimations() {
    this.prefersReducedMotion = true;
  }

  // Public method to enable animations
  enableAnimations() {
    this.prefersReducedMotion = false;
  }

  // Holographic effect helper methods
  updateHolographicColors(badge, deltaX, deltaY) {
    // Calculate dynamic hue shift based on mouse position
    const hueShift = (deltaX + deltaY) * 60; // Range: -120 to 120 degrees
    const intensity = Math.min(0.9, 0.6 + Math.abs(deltaX + deltaY) * 0.3);

    badge.style.setProperty("--holo-primary", `${hueShift}deg`);
    badge.style.setProperty("--holo-secondary", `${120 + hueShift}deg`);
    badge.style.setProperty("--holo-tertiary", `${240 + hueShift}deg`);
    badge.style.setProperty("--holo-intensity", intensity);
    badge.style.setProperty("--holo-speed", "1s");
  }

  getHolographicGlow(deltaX, deltaY) {
    // Generate dynamic holographic glow color
    const hue = (deltaX + deltaY) * 60 + 180;
    const saturation = 70 + Math.abs(deltaX * deltaY) * 30;
    const lightness = 60 + Math.abs(deltaX + deltaY) * 20;
    return `hsla(${hue}, ${saturation}%, ${lightness}%, 0.4)`;
  }

  updateLightRayPosition(badge, deltaX, deltaY) {
    const lightRay = badge.querySelector(".light-ray");
    if (lightRay) {
      // Position light ray to follow mouse movement
      const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
      lightRay.style.transform = `rotate(${angle}deg) translateX(${
        deltaX * 20
      }px)`;
      lightRay.style.opacity = Math.abs(deltaX + deltaY) * 0.5;
    }
  }

  // Enhanced method to trigger holographic pulse effect
  triggerHolographicPulse(badge) {
    if (this.prefersReducedMotion) return;

    badge.classList.add("active");
    setTimeout(() => {
      badge.classList.remove("active");
    }, 2000);
  }
}
