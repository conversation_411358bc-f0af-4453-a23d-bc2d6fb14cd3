import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="badge"
export default class extends Controller {
  static targets = ["badge"];

  connect() {
    this.checkReducedMotion();
    this.addEventListeners();
    this.initializeHolographicEffects();
  }

  disconnect() {
    this.removeEventListeners();
  }

  addEventListeners() {
    this.badgeTargets.forEach((badge) => {
      badge.addEventListener("mousemove", this.handleMouseMove.bind(this));
      badge.addEventListener("mouseleave", this.handleMouseLeave.bind(this));
      badge.addEventListener("mouseenter", this.handleMouseEnter.bind(this));
    });
  }

  removeEventListeners() {
    this.badgeTargets.forEach((badge) => {
      badge.removeEventListener("mousemove", this.handleMouseMove.bind(this));
      badge.removeEventListener("mouseleave", this.handleMouseLeave.bind(this));
      badge.removeEventListener("mouseenter", this.handleMouseEnter.bind(this));
    });
  }

  checkReducedMotion() {
    // Check if user prefers reduced motion for accessibility
    this.prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
  }

  initializeHolographicEffects() {
    if (this.prefersReducedMotion) return;
    // Holographic effects are now handled via CSS :before and :after pseudo-elements
    // No need to create DOM elements
  }

  handleMouseMove(e) {
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    const rect = badge.getBoundingClientRect();

    // Calculate mouse position relative to badge
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const w = rect.width;
    const h = rect.height;

    // Convert to percentages (0-100)
    const px = Math.abs(Math.floor((100 / w) * x) - 100);
    const py = Math.abs(Math.floor((100 / h) * y) - 100);

    // Calculate gradient positions for holographic effect
    const lp = 50 + (px - 50) / 1.5;
    const tp = 50 + (py - 50) / 1.5;

    // Calculate 3D transform values
    const ty = ((tp - 50) / 2) * -1;
    const tx = ((lp - 50) / 1.5) * 0.5;

    // Calculate opacity based on distance from center
    const pa = 50 - px + (50 - py);
    const opacity = Math.min(1, (20 + Math.abs(pa) * 1.5) / 100);

    // Apply dynamic styles using CSS custom properties
    badge.style.setProperty("--holo-x", `${lp}%`);
    badge.style.setProperty("--holo-y", `${tp}%`);
    badge.style.setProperty("--holo-opacity", opacity);
    badge.style.transform = `rotateX(${ty}deg) rotateY(${tx}deg)`;

    // Add active class for enhanced effects
    badge.classList.add("holo-active");
  }

  handleMouseEnter(e) {
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    badge.classList.add("holo-active");
  }

  handleMouseLeave(e) {
    if (this.prefersReducedMotion) return;

    const badge = e.currentTarget;
    badge.classList.remove("holo-active");

    // Reset transform and custom properties
    badge.style.transform = "";
    badge.style.removeProperty("--holo-x");
    badge.style.removeProperty("--holo-y");
    badge.style.removeProperty("--holo-opacity");
  }
}
