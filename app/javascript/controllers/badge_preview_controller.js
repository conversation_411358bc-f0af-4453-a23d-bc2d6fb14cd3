import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="badge-preview"
export default class extends Controller {
  static targets = [
    "preview", // The preview badge element
    "name", // Name input field
    "description", // Description input field
    "backgroundColor", // Background color input
    "textColor", // Text color input
    "icon", // Icon input field
    "priority", // Priority input field
  ];

  static values = {
    defaultName: { type: String, default: "Sample Badge" },
    defaultDescription: {
      type: String,
      default: "This is a sample badge description",
    },
    defaultBackgroundColor: { type: String, default: "#3B82F6" },
    defaultTextColor: { type: String, default: "#FFFFFF" },
    defaultIcon: { type: String, default: "fas fa-star" },
  };

  connect() {
    console.log("BadgePreviewController connected");

    // Initialize preview with current values or defaults
    this.updatePreview();

    // Set up event listeners for real-time updates
    this.setupEventListeners();
  }

  disconnect() {
    // Clean up event listeners
    this.removeEventListeners();
  }

  setupEventListeners() {
    // Listen for input events on all form fields
    if (this.hasNameTarget) {
      this.nameTarget.addEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasDescriptionTarget) {
      this.descriptionTarget.addEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasBackgroundColorTarget) {
      this.backgroundColorTarget.addEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
      this.backgroundColorTarget.addEventListener(
        "change",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasTextColorTarget) {
      this.textColorTarget.addEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
      this.textColorTarget.addEventListener(
        "change",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasIconTarget) {
      this.iconTarget.addEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasPriorityTarget) {
      this.priorityTarget.addEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }
  }

  removeEventListeners() {
    // Remove event listeners to prevent memory leaks
    if (this.hasNameTarget) {
      this.nameTarget.removeEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasDescriptionTarget) {
      this.descriptionTarget.removeEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasBackgroundColorTarget) {
      this.backgroundColorTarget.removeEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
      this.backgroundColorTarget.removeEventListener(
        "change",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasTextColorTarget) {
      this.textColorTarget.removeEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
      this.textColorTarget.removeEventListener(
        "change",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasIconTarget) {
      this.iconTarget.removeEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }

    if (this.hasPriorityTarget) {
      this.priorityTarget.removeEventListener(
        "input",
        this.handleInputChange.bind(this)
      );
    }
  }

  handleInputChange(event) {
    // Debounce the update to avoid excessive DOM manipulation
    clearTimeout(this.updateTimeout);
    this.updateTimeout = setTimeout(() => {
      this.updatePreview();
    }, 100);
  }

  updatePreview() {
    if (!this.hasPreviewTarget) {
      console.warn("Badge preview target not found");
      return;
    }

    // Get current values from form fields or use defaults
    const name = this.getCurrentValue("name", this.defaultNameValue);
    const description = this.getCurrentValue(
      "description",
      this.defaultDescriptionValue
    );
    const backgroundColor = this.getCurrentValue(
      "backgroundColor",
      this.defaultBackgroundColorValue
    );
    const textColor = this.getCurrentValue(
      "textColor",
      this.defaultTextColorValue
    );
    const icon = this.getCurrentValue("icon", this.defaultIconValue);

    // Update the preview badge appearance
    this.updateBadgeAppearance(name, backgroundColor, textColor, icon);

    // Update the description display
    this.updateDescriptionDisplay(description);

    // Validate color contrast and show warnings if needed
    this.validateColorContrast(backgroundColor, textColor);
  }

  getCurrentValue(fieldName, defaultValue) {
    const targetName = `${fieldName}Target`;
    if (
      this[`has${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}Target`]
    ) {
      const value = this[targetName].value;
      return value && value.trim() !== "" ? value : defaultValue;
    }
    return defaultValue;
  }

  updateBadgeAppearance(name, backgroundColor, textColor, icon) {
    // Update all badge preview elements (both full size and compact)
    const badgeElements = this.previewTarget.querySelectorAll(
      ".badge-preview-element"
    );
    const iconElements = this.previewTarget.querySelectorAll(
      ".badge-preview-icon"
    );
    const nameElements = this.previewTarget.querySelectorAll(
      ".badge-preview-name"
    );

    badgeElements.forEach((badgeElement) => {
      if (badgeElement) {
        // Update badge styles
        badgeElement.style.backgroundColor = backgroundColor;
        badgeElement.style.color = textColor;
        badgeElement.style.borderColor = this.adjustColorBrightness(
          backgroundColor,
          -20
        );
      }
    });

    iconElements.forEach((iconElement) => {
      if (iconElement && icon) {
        // Update icon classes, preserving existing classes
        const existingClasses = iconElement.className
          .split(" ")
          .filter(
            (cls) =>
              cls.includes("badge-preview-icon") ||
              cls.includes("mr-") ||
              cls.includes("text-")
          );
        iconElement.className = `badge-preview-icon ${icon} ${existingClasses
          .filter((cls) => cls.includes("mr-") || cls.includes("text-"))
          .join(" ")}`;
      }
    });

    nameElements.forEach((nameElement) => {
      if (nameElement) {
        // Update badge name
        nameElement.textContent = name || this.defaultNameValue;
      }
    });

    // Update property summary section
    this.updatePropertySummary(backgroundColor, textColor, icon);
  }

  updateDescriptionDisplay(description) {
    const descriptionElement = this.previewTarget.querySelector(
      ".badge-preview-description"
    );
    if (descriptionElement) {
      descriptionElement.textContent =
        description || this.defaultDescriptionValue;
    }
  }

  updatePropertySummary(backgroundColor, textColor, icon) {
    // Update background color swatch and value
    const bgColorElement = this.previewTarget.querySelector(
      ".badge-preview-bg-color"
    );
    const bgValueElement = this.previewTarget.querySelector(
      ".badge-preview-bg-value"
    );
    if (bgColorElement) bgColorElement.style.backgroundColor = backgroundColor;
    if (bgValueElement) bgValueElement.textContent = backgroundColor;

    // Update text color swatch and value
    const textColorElement = this.previewTarget.querySelector(
      ".badge-preview-text-color"
    );
    const textValueElement = this.previewTarget.querySelector(
      ".badge-preview-text-value"
    );
    if (textColorElement) textColorElement.style.backgroundColor = textColor;
    if (textValueElement) textValueElement.textContent = textColor;

    // Update icon value
    const iconValueElement = this.previewTarget.querySelector(
      ".badge-preview-icon-value"
    );
    if (iconValueElement)
      iconValueElement.textContent = icon || this.defaultIconValue;

    // Update priority value
    const priority = this.getCurrentValue("priority", "0");
    const priorityValueElement = this.previewTarget.querySelector(
      ".badge-preview-priority-value"
    );
    if (priorityValueElement) priorityValueElement.textContent = priority;
  }

  validateColorContrast(backgroundColor, textColor) {
    const contrastRatio = this.calculateContrastRatio(
      backgroundColor,
      textColor
    );
    const warningElement =
      this.previewTarget.querySelector(".contrast-warning");

    if (warningElement) {
      if (contrastRatio < 4.5) {
        warningElement.classList.remove("hidden");
        warningElement.textContent = `Low contrast ratio: ${contrastRatio.toFixed(
          2
        )}. Consider adjusting colors for better accessibility.`;
      } else {
        warningElement.classList.add("hidden");
      }
    }
  }

  // Utility method to calculate color contrast ratio
  calculateContrastRatio(color1, color2) {
    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);

    const brightest = Math.max(luminance1, luminance2);
    const darkest = Math.min(luminance1, luminance2);

    return (brightest + 0.05) / (darkest + 0.05);
  }

  // Get relative luminance of a color
  getLuminance(hexColor) {
    const rgb = this.hexToRgb(hexColor);
    if (!rgb) return 0;

    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map((c) => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  // Convert hex color to RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  }

  // Adjust color brightness for border effects
  adjustColorBrightness(hexColor, percent) {
    const rgb = this.hexToRgb(hexColor);
    if (!rgb) return hexColor;

    const adjust = (color) => {
      const adjusted = Math.round(color + (color * percent) / 100);
      return Math.max(0, Math.min(255, adjusted));
    };

    const r = adjust(rgb.r).toString(16).padStart(2, "0");
    const g = adjust(rgb.g).toString(16).padStart(2, "0");
    const b = adjust(rgb.b).toString(16).padStart(2, "0");

    return `#${r}${g}${b}`;
  }

  // Action methods that can be called from the template
  resetPreview() {
    // Reset all form fields to default values
    if (this.hasNameTarget) this.nameTarget.value = this.defaultNameValue;
    if (this.hasDescriptionTarget)
      this.descriptionTarget.value = this.defaultDescriptionValue;
    if (this.hasBackgroundColorTarget)
      this.backgroundColorTarget.value = this.defaultBackgroundColorValue;
    if (this.hasTextColorTarget)
      this.textColorTarget.value = this.defaultTextColorValue;
    if (this.hasIconTarget) this.iconTarget.value = this.defaultIconValue;

    // Update the preview
    this.updatePreview();
  }

  // Method to manually trigger preview update (useful for external calls)
  refresh() {
    this.updatePreview();
  }
}
